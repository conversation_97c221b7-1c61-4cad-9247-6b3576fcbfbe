import React, { useState, useEffect } from 'react';
import { useSelector } from 'react-redux';
import UserNav from "../Components/UserNav";
import { IoTrendingUp, IoSearch, IoClose } from "react-icons/io5";
import Post from "../Components/Post";

const Discover = () => {
    const [searchQuery, setSearchQuery] = useState('');
    const [selectedTags, setSelectedTags] = useState([]);
    const [filteredPosts, setFilteredPosts] = useState([]);
    
    const posts = useSelector((state) => state.posts);
    const tandances = useSelector((state) => state.tandances);

    // Get all unique tags from posts
    const allTags = [...new Set(posts.flatMap(post => post.hashtags || []))];

    // Filter posts based on search query and selected tags
    useEffect(() => {
        let filtered = [...posts];

        if (searchQuery) {
            filtered = filtered.filter(post => 
                post.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
                post.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                post.place.toLowerCase().includes(searchQuery.toLowerCase()) ||
                (post.hashtags || []).some(tag => 
                    tag.toLowerCase().includes(searchQuery.toLowerCase())
                )
            );
        }

        if (selectedTags.length > 0) {
            filtered = filtered.filter(post =>
                selectedTags.every(tag => 
                    (post.hashtags || []).includes(tag)
                )
            );
        }

        setFilteredPosts(filtered);
    }, [searchQuery, selectedTags, posts]);

    const toggleTag = (tag) => {
        setSelectedTags(prev => 
            prev.includes(tag)
                ? prev.filter(t => t !== tag)
                : [...prev, tag]
        );
    };

    return (
        <section className="min-h-screen bg-gray">
            <UserNav />
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                {/* Search and Filters Section */}
                <div className="bg-white rounded-xl shadow-sm p-6 mb-6">
                    <div className="flex flex-col gap-6">
                        {/* Search Bar */}
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Search posts, users, or hashtags..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-12 pr-4 py-3 rounded-xl bg-gray border-none focus:ring-2 focus:ring-secondary/50"
                            />
                            <IoSearch className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 text-xl" />
                            {searchQuery && (
                                <button
                                    onClick={() => setSearchQuery('')}
                                    className="absolute right-4 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                                >
                                    <IoClose className="text-xl" />
                                </button>
                            )}
                        </div>

                        {/* Tags */}
                        <div>
                            <h3 className="text-gray-700 font-medium mb-3">Popular Tags</h3>
                            <div className="flex flex-wrap gap-2">
                                {allTags.map((tag) => (
                                    <button
                                        key={tag}
                                        onClick={() => toggleTag(tag)}
                                        className={`px-4 py-2 rounded-full text-sm font-medium transition-all duration-200
                                            ${selectedTags.includes(tag)
                                                ? 'bg-secondary text-white'
                                                : 'bg-gray text-gray-600 hover:bg-gray-200'
                                            }`}
                                    >
                                        #{tag}
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Content Grid */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Posts Column */}
                    <div className="lg:col-span-2 space-y-6">
                        <h2 className="text-xl font-semibold text-gray-800 mb-4">
                            {searchQuery || selectedTags.length > 0 
                                ? 'Search Results' 
                                : 'Discover Posts'}
                        </h2>
                        {filteredPosts.length > 0 ? (
                            filteredPosts.map((post) => (
                                <Post key={post.id} post={post} />
                            ))
                        ) : (
                            <div className="text-center py-12 bg-white rounded-xl">
                                <div className="inline-block p-4 rounded-full bg-gray-100 mb-4">
                                    <IoSearch className="w-8 h-8 text-gray-400" />
                                </div>
                                <h3 className="text-lg font-medium text-gray-900">No posts found</h3>
                                <p className="mt-2 text-gray-500">
                                    Try adjusting your search or filters
                                </p>
                            </div>
                        )}
                    </div>

                    {/* Trending Column */}
                    <div className="lg:col-span-1">
                        <div className="bg-white rounded-xl shadow-sm p-6 sticky top-24">
                            <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center gap-2">
                                <IoTrendingUp className="text-secondary" />
                                Trending
                            </h2>
                            <div className="space-y-4">
                                {tandances.map((trend) => (
                                    <button
                                        key={trend.id}
                                        onClick={() => setSearchQuery(trend.title)}
                                        className="w-full text-left p-3 rounded-xl hover:bg-gray transition-colors duration-200"
                                    >
                                        <div className="flex items-center justify-between">
                                            <span className="text-secondary">#{trend.title}</span>
                                            <span className="text-sm text-gray-500">
                                                {trend.count} posts
                                            </span>
                                        </div>
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default Discover;