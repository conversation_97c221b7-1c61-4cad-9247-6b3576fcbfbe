import { FaHome } from "react-icons/fa";
import { Link, NavLink } from "react-router-dom";
import { IoIosAddCircleOutline } from "react-icons/io";
import { IoBookmark, IoHeart, IoPersonCircleOutline, IoCompass } from "react-icons/io5";
import { useSelector } from "react-redux";
import TandancesCard from "./TandancesCard";

const Navbar = () => {

    const tandances = useSelector((state) => state.tandances);
    return (
        <header className="w-64 fixed h-screen border-r border-gray-100 bg-white">
            <div className="p-6 h-full flex flex-col">
                {/* Logo */}
                <img src="/logo.png" alt="Logo" className="w-[180px] h-auto mb-8" />
                
                <nav className="flex-1">
                    <ul className="space-y-2 p-0 mb-6 border-b border-gray-100 pb-6">
                        {/* Add <PERSON>ton */}
                        <li>
                            <Link to="/create-post" className="w-full bg-secondary hover:bg-secondary-dark transition-all duration-300 text-white rounded-xl p-3 flex items-center gap-3 font-semibold shadow-sm hover:shadow-md">
                                <IoIosAddCircleOutline className="text-2xl" />
                                <span>Create Post</span>
                            </Link>
                        </li>
                        
                        {/* Navigation Links */}
                        <li>
                            <NavLink 
                                to="/" 
                                className={({ isActive }) =>
                                    `w-full p-3 flex items-center gap-3 rounded-xl transition-all duration-200 ${
                                        isActive 
                                            ? 'bg-gray text-secondary font-semibold' 
                                            : 'text-gray-600 hover:bg-gray-50'
                                    }`
                                }
                            >
                                <FaHome className="text-xl" />
                                <span>Home</span>
                            </NavLink>
                        </li>

                        {/* Add Discover Link */}
                        <li>
                            <NavLink 
                                to="/discover" 
                                className={({ isActive }) =>
                                    `w-full p-3 flex items-center gap-3 rounded-xl transition-all duration-200 ${
                                        isActive 
                                            ? 'bg-gray text-secondary font-semibold' 
                                            : 'text-gray-600 hover:bg-gray-50'
                                    }`
                                }
                            >
                                <IoCompass className="text-xl" />
                                <span>Discover</span>
                            </NavLink>
                        </li>
                        
                        <li>
                            <NavLink 
                                to="/profile" 
                                className={({ isActive }) =>
                                    `w-full p-3 flex items-center gap-3 rounded-xl transition-all duration-200 ${
                                        isActive 
                                            ? 'bg-gray text-secondary font-semibold' 
                                            : 'text-gray-600 hover:bg-gray-50'
                                    }`
                                }
                            >
                                <IoPersonCircleOutline className="text-xl" />
                                <span>Profile</span>
                            </NavLink>
                        </li>
                        
                       
                    </ul>

                    {/* Trends Section */}
                    <div className="space-y-4">
                        <h2 className="text-xl font-semibold text-gray-800">Trends</h2>
                        <div className="space-y-3">
                            {tandances.map((el) => (
                                <TandancesCard key={el.id} el={el} />
                            ))}
                        </div>
                    </div>
                </nav>
            </div>
        </header>
    );
}

export default Navbar;
